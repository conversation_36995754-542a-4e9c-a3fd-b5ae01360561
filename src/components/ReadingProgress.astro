---
// Reading Progress Bar Component
// Shows reading progress at the top of the page
---

<div id="reading-progress-container" class="reading-progress-container">
	<div id="reading-progress-bar" class="reading-progress-bar"></div>
</div>

<style>
	.reading-progress-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 3px;
		background-color: rgba(0, 0, 0, 0.05);
		z-index: 1000;
		opacity: 0;
		transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		backdrop-filter: blur(8px);
		-webkit-backdrop-filter: blur(8px);
	}

	.reading-progress-container.visible {
		opacity: 1;
	}

	.reading-progress-bar {
		height: 100%;
		width: 0%;
		background: linear-gradient(
			90deg,
			#3b82f6 0%,
			#06b6d4 25%,
			#10b981 50%,
			#f59e0b 75%,
			#ef4444 100%
		);
		transition: width 0.15s cubic-bezier(0.4, 0, 0.2, 1);
		border-radius: 0 2px 2px 0;
		box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
		position: relative;
		overflow: hidden;
	}

	.reading-progress-bar::after {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(
			90deg,
			transparent 0%,
			rgba(255, 255, 255, 0.3) 50%,
			transparent 100%
		);
		animation: shimmer 2s infinite;
	}

	.reading-progress-bar.pulse {
		animation: pulse 0.6s ease-in-out;
	}

	@keyframes shimmer {
		0% {
			transform: translateX(-100%);
		}
		100% {
			transform: translateX(100%);
		}
	}

	@keyframes pulse {
		0% {
			box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
		}
		50% {
			box-shadow: 0 0 16px rgba(59, 130, 246, 0.6);
		}
		100% {
			box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
		}
	}

	/* Dark mode support */
	:global([data-theme="dark"]) .reading-progress-container {
		background-color: rgba(255, 255, 255, 0.05);
	}

	:global([data-theme="dark"]) .reading-progress-bar {
		box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
	}

	/* Responsive adjustments */
	@media (max-width: 640px) {
		.reading-progress-container {
			height: 2px;
		}

		.reading-progress-bar {
			box-shadow: 0 0 4px rgba(59, 130, 246, 0.3);
		}
	}

	/* Reduce motion for users who prefer it */
	@media (prefers-reduced-motion: reduce) {
		.reading-progress-container {
			transition: opacity 0.1s linear;
		}

		.reading-progress-bar {
			transition: width 0.05s linear;
		}

		.reading-progress-bar::after {
			animation: none;
		}
	}

	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.reading-progress-container {
			background-color: rgba(0, 0, 0, 0.2);
		}

		.reading-progress-bar {
			background: #000;
			box-shadow: none;
		}

		:global([data-theme="dark"]) .reading-progress-container {
			background-color: rgba(255, 255, 255, 0.2);
		}

		:global([data-theme="dark"]) .reading-progress-bar {
			background: #fff;
		}
	}
</style>

<script>
	class ReadingProgress extends HTMLElement {
		private progressBar: HTMLElement | null = null;
		private progressContainer: HTMLElement | null = null;
		private article: HTMLElement | null = null;
		private isVisible = false;
		private ticking = false;
		private scrollTimeout: number | null = null;

		constructor() {
			super();
		}

		connectedCallback() {
			this.init();
		}

		private init() {
			// Get DOM elements
			this.progressBar = document.getElementById("reading-progress-bar");
			this.progressContainer = document.getElementById(
				"reading-progress-container",
			);
			this.article = document.querySelector(
				"article[data-pagefind-body]",
			);

			if (!this.progressBar || !this.progressContainer || !this.article) {
				console.warn("Reading Progress: Required elements not found");
				return;
			}

			// Bind scroll event with throttling
			this.bindScrollEvent();
		}

		private bindScrollEvent() {
			window.addEventListener(
				"scroll",
				() => {
					if (!this.ticking) {
						requestAnimationFrame(() => {
							this.updateProgress();
							this.ticking = false;
						});
						this.ticking = true;
					}

					// Clear existing timeout
					if (this.scrollTimeout) {
						clearTimeout(this.scrollTimeout);
					}

					// Remove pulse effect while scrolling
					this.progressBar?.classList.remove("pulse");

					// Add pulse effect when scrolling stops
					this.scrollTimeout = window.setTimeout(() => {
						this.progressBar?.classList.add("pulse");
						// Remove pulse after animation
						setTimeout(() => {
							this.progressBar?.classList.remove("pulse");
						}, 1000);
					}, 150);
				},
				{ passive: true },
			);

			// Initial check
			this.updateProgress();
		}

		private updateProgress() {
			if (!this.article || !this.progressBar || !this.progressContainer)
				return;

			const windowHeight = window.innerHeight;
			const scrollTop =
				window.pageYOffset || document.documentElement.scrollTop;

			// Get article position relative to document
			const articleRect = this.article.getBoundingClientRect();
			const articleTop = articleRect.top + scrollTop;
			const articleHeight = this.article.offsetHeight;
			const articleBottom = articleTop + articleHeight;

			// Show progress bar when we start reading the article
			// Start showing slightly before reaching the article
			const shouldShow = scrollTop > articleTop - windowHeight * 0.2;

			if (shouldShow !== this.isVisible) {
				this.isVisible = shouldShow;
				this.progressContainer.classList.toggle("visible", shouldShow);
			}

			if (shouldShow) {
				// Calculate reading progress based on article content
				const articleStart = articleTop;
				const currentPosition = scrollTop + windowHeight; // Current bottom of viewport

				let progress = 0;

				if (currentPosition <= articleStart + windowHeight) {
					progress = 0;
				} else if (currentPosition >= articleBottom) {
					// When viewport bottom reaches article bottom, we're at 100%
					progress = 100;
				} else {
					// Linear interpolation between start and end
					const totalDistance =
						articleBottom - (articleStart + windowHeight);
					const currentDistance =
						currentPosition - (articleStart + windowHeight);
					progress = (currentDistance / totalDistance) * 100;
				}

				// Ensure progress is between 0 and 100
				progress = Math.max(0, Math.min(100, progress));

				// Update progress bar width with smooth transition
				this.progressBar.style.width = `${progress.toFixed(1)}%`;
			}
		}
	}

	// Register the custom element
	customElements.define("reading-progress", ReadingProgress);
</script>

<reading-progress></reading-progress>
